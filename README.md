# ES-Forker: Elasticsearch Index Forking Tool

This tool allows you to fork (reindex) an Elasticsearch index to another index, optionally with custom settings, mappings, and a query filter.

## Features
- Reindex between local or remote Elasticsearch clusters
- Custom destination index settings and mappings
- Query-based document filtering
- Dry-run mode for operation preview
- Safety prompts for existing index overwrite
- Detailed progress logging

## Installation
```bash
pip install elasticsearch
```

## Usage
```bash
python fork_es_index.py
```

### Required Parameters
| Parameter      | Description                          |
|----------------|--------------------------------------|
| `source_index` | Source index name                   |
| `dest_index`   | Destination index name              |

### Optional Parameters
| Parameter         | Default       | Description                                      |
|-------------------|---------------|--------------------------------------------------|
| `es_host`         | `localhost`   | Elasticsearch host                              |
| `es_port`         | `9200`        | Elasticsearch port                              |
| `es_user`         | `None`        | Username for authenticated clusters             |
| `es_password`     | `None`        | Password for authenticated clusters             |
| `query_body`      | `None`        | JSON query to filter documents (e.g., `{"match": {"status": "active"}}`) |
| `dest_settings`   | `None`        | Custom destination index settings               |
| `dest_mappings`   | `None`        | Custom destination index mappings               |
| `request_timeout` | `3600`        | Operation timeout in seconds (1 hour default)   |
| `dry_run`         | `False`       | Preview without executing (True/False)          |

## Safety Features
- Confirmation prompt before overwriting existing indices
- Detailed operation preview in dry-run mode
- Comprehensive error handling and logging

## Notes
- Ensure network access to source/destination clusters
- For large indices, increase `request_timeout` value
