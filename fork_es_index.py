from elasticsearch import Elasticsearch
from elasticsearch.helpers import reindex
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def es_reindex(
    source_index: str,
    dest_index: str,
    es_host: str = "localhost",
    es_port: int = 9200,
    es_user: str = None,
    es_password: str = None,
    query_body: dict = None,
    dest_settings: dict = None,
    dest_mappings: dict = None,
    request_timeout: int = 3600, # 默认超时时间为1小时
    dry_run: bool = False
):
    """
    将数据从一个 Elasticsearch 索引重新索引到另一个索引。

    Args:
        source_index (str): 源索引的名称。
        dest_index (str): 目标索引的名称。
        es_host (str): Elasticsearch 主机名。默认为 "localhost"。
        es_port (int): Elasticsearch 端口。默认为 9200。
        es_user (str): Elasticsearch 用户名（如果启用安全）。
        es_password (str): Elasticsearch 密码（如果启用安全）。
        query_body (dict): 可选的查询体，用于过滤要重新索引的文档。
                          例如: {"match": {"field_name": "value"}}
        dest_settings (dict): 可选的目标索引设置。
                             例如: {"number_of_shards": 1, "number_of_replicas": 0}
        dest_mappings (dict): 可选的目标索引映射。
                             例如: {"properties": {"new_field": {"type": "keyword"}}}
        request_timeout (int): 请求超时时间（秒）。
        dry_run (bool): 如果为 True，则只打印将要执行的操作，不实际执行 reindex。
    """

    # 构建 Elasticsearch 客户端连接信息

    try:
        es_url = f"http://{es_host}:{es_port}"
        es = Elasticsearch(es_url, basic_auth=(es_user, es_password), request_timeout=request_timeout)
        if not es.ping():
            logging.error(f"无法连接到 Elasticsearch 集群: {es_host}:{es_port}")
            return

        logging.info(f"成功连接到 Elasticsearch 集群: {es_host}:{es_port}")

        # 检查源索引是否存在
        if not es.indices.exists(index=source_index):
            logging.error(f"源索引 '{source_index}' 不存在。请检查索引名称。")
            return

        # 如果目标索引存在，需要用户确认是否覆盖或退出
        if es.indices.exists(index=dest_index):
            logging.warning(f"目标索引 '{dest_index}' 已经存在。")
            if not dry_run:
                confirm = input(f"是否删除现有目标索引 '{dest_index}' 并继续重新索引？(yes/no): ").lower()
                if confirm != 'yes':
                    logging.info("用户取消了操作。")
                    return
                logging.info(f"正在删除现有目标索引 '{dest_index}'...")
                es.indices.delete(index=dest_index, ignore=[400, 404])
                logging.info(f"目标索引 '{dest_index}' 已删除。")

        # 创建目标索引 (如果指定了设置或映射)
        if dest_settings or dest_mappings:
            logging.info(f"正在使用自定义设置和映射创建目标索引 '{dest_index}'...")
            index_body = {}
            if dest_settings:
                index_body['settings'] = dest_settings
            if dest_mappings:
                index_body['mappings'] = dest_mappings
            
            if not dry_run:
                es.indices.create(index=dest_index, body=index_body, ignore=400) # ignore 400 if index already exists
                logging.info(f"目标索引 '{dest_index}' 已创建（或已存在）。")
            else:
                logging.info(f"[Dry Run] 将会使用以下设置和映射创建目标索引 '{dest_index}':\n{index_body}")
        else:
            logging.info(f"目标索引 '{dest_index}' 将在 reindex 过程中自动创建（使用源索引的默认设置和映射）。")


        # 构建 reindex API 请求体
        reindex_body = {
            "source": {
                "index": source_index
            },
            "dest": {
                "index": dest_index
            }
        }

        if query_body:
            reindex_body["source"]["query"] = query_body

        logging.info(f"准备重新索引从 '{source_index}' 到 '{dest_index}'。")
        if query_body:
            logging.info(f"过滤条件: {query_body}")

        if dry_run:
            logging.info(f"[Dry Run] 将会执行以下 reindex 操作:\n{reindex_body}")
            logging.info("这是试运行，没有实际执行 reindex。")
            return

        # 执行 reindex 操作
        logging.info("开始执行 reindex 操作，这可能需要一段时间...")
        response = es.reindex(body=reindex_body, wait_for_completion=True)

        if response.get('failures'):
            logging.error(f"Reindex 操作完成，但存在失败: {response.get('failures')}")
        else:
            logging.info("Reindex 操作成功完成！")
            logging.info(f"文档复制: {response.get('total')} 个")
            logging.info(f"创建文档: {response.get('created')} 个")
            logging.info(f"更新文档: {response.get('updated')} 个")
            logging.info(f"删除冲突: {response.get('deleted')} 个")
            logging.info(f"总耗时: {response.get('took')} 毫秒")

    except Exception as e:
        logging.error(f"发生错误: {e}")

if __name__ == "__main__":
    # --- 示例用法 ---

    # 1. 最简单的 reindex：将 'my_source_index' 的所有文档复制到 'my_dest_index'
    # es_reindex(
    #     source_index="my_source_index",
    #     dest_index="my_dest_index"
    # )

    # 2. 带过滤条件的 reindex：只复制 'status' 字段为 'active' 的文档
    # es_reindex(
    #     source_index="my_source_index",
    #     dest_index="my_active_documents",
    #     query_body={
    #         "match": {
    #             "status": "active"
    #         }
    #     }
    # )

    # 3. 重新索引并改变目标索引的设置和映射
    # es_reindex(
    #     source_index="my_source_index",
    #     dest_index="my_new_index_with_custom_settings",
    #     dest_settings={
    #         "number_of_shards": 1,
    #         "number_of_replicas": 0
    #     },
    #     dest_mappings={
    #         "properties": {
    #             "original_field": {"type": "keyword"},
    #             "new_field": {"type": "text"}
    #         }
    #     }
    # )

    # 4. 连接到远程 Elasticsearch 集群并使用认证
    # es_reindex(
    #     source_index="production_data",
    #     dest_index="staging_data",
    #     es_host="your-es-host.com",
    #     es_port=9200,
    #     es_user="elastic",
    #     es_password="your_password",
    #     request_timeout=120 # 设置更短的超时
    # )

    # 5. 试运行 (Dry Run)：查看会执行什么操作，但不实际修改数据
    # es_reindex(
    #     source_index="mini_program_claireysun_356app_base",
    #     dest_index="mini_program_claireysun_356app_base_forked_by_ronny",
    #     es_host="***********",
    #     es_port=9200,
    #     es_user="elastic",
    #     es_password="Idnetsirk064luban",
    #     dry_run=True
    # )

    # 6. 真正的去执行操作
    es_reindex(
        source_index="mini_program_claireysun_356app_base",
        dest_index="mini_program_claireysun_356app_base_forked_by_ronny",
        es_host="***********",
        es_port=9200,
        es_user="elastic",
        es_password="Idnetsirk064luban",
        dry_run=False
    )